[gd_scene load_steps=38 format=4 uid="uid://diggs2d11miw"]

[ext_resource type="Texture2D" uid="uid://btvp3uwoev8sh" path="res://chapters/chapter 1/environment sprites/tilemap.png" id="1_kh6sw"]
[ext_resource type="PackedScene" uid="uid://fdb8t12ej0n1" path="res://player/player.tscn" id="2_nn7wm"]
[ext_resource type="Texture2D" uid="uid://c2eq61hgax2ss" path="res://chapters/chapter 1/environment sprites/background 2.png" id="2_r1lq4"]
[ext_resource type="Texture2D" uid="uid://bfk28a3pf8ply" path="res://chapters/chapter 1/environment sprites/background 3.png" id="3_id86x"]
[ext_resource type="Texture2D" uid="uid://ipb311v4n477" path="res://chapters/chapter 1/environment sprites/background.png" id="3_je4n3"]
[ext_resource type="Texture2D" uid="uid://bw66tbhphpctr" path="res://chapters/chapter 1/environment sprites/background 4.png" id="3_stnnl"]
[ext_resource type="Texture2D" uid="uid://dnmnkyaqcx2me" path="res://chapters/chapter 1/environment sprites/background 5.png" id="6_2fp7s"]
[ext_resource type="Texture2D" uid="uid://cwa54gjt06k5k" path="res://chapters/chapter 1/environment sprites/background 6.png" id="7_qmn82"]
[ext_resource type="Texture2D" uid="uid://dp2g0wix1wrb2" path="res://chapters/chapter 1/environment sprites/background 7.png" id="9_wkftq"]
[ext_resource type="Texture2D" uid="uid://dgisq8o05nw2u" path="res://chapters/chapter 1/npc sprites/Sign.png" id="10_dy8eh"]
[ext_resource type="FontFile" uid="uid://231c8iuwhd1b" path="res://Poppins/Poppins-Black.ttf" id="10_ej2fq"]
[ext_resource type="PackedScene" uid="uid://bxx41bcyxworf" path="res://NPC/npc.tscn" id="10_pdrt4"]
[ext_resource type="Texture2D" uid="uid://booitnykv0plw" path="res://chapters/chapter 1/npc sprites/npc 1.png" id="11_w6op7"]
[ext_resource type="Texture2D" uid="uid://bqbhs3jqurxs8" path="res://chapters/chapter 1/npc sprites/npc 2.png" id="12_2uve2"]
[ext_resource type="Texture2D" uid="uid://cm14xw1406u14" path="res://chapters/chapter 1/environment sprites/factory.png" id="15_unyb1"]
[ext_resource type="PackedScene" uid="uid://cxog5psgyyt40" path="res://next level teleporter/next_level_teleporter.tscn" id="16_jddgd"]
[ext_resource type="PackedScene" uid="uid://c84s6iru81fqd" path="res://chapters/chapter 2/chapter_2.tscn" id="17_t7dqo"]

[sub_resource type="SpriteFrames" id="SpriteFrames_ej2fq"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("10_dy8eh")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_dy8eh"]
size = Vector2(171, 72)

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_nn7wm"]
texture = ExtResource("1_kh6sw")
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-7.0776024, 6.991639, -6.0007696, 6.9874196, -6.1033573, 8, 8, 8, 8, -8, -7.020294, -8)
1:0/0 = 0
1:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-6.962986, -8, -7.020294, 8, 8, 8, 8, -8)
1:1/0 = 0
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:2/0 = 0
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-6.962986, -8, -7.020294, 8, 8, 8, 8, -8)
1:2/0 = 0
1:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:0/0 = 0
0:3/0 = 0
0:4/0 = 0
0:5/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
3:0/0 = 0
1:3/0 = 0
1:4/0 = 0
2:4/0 = 0
2:3/0 = 0
3:3/0 = 0
3:4/0 = 0
3:5/0 = 0
2:5/0 = 0
1:5/0 = 0
2:2/0 = 0
3:2/0 = 0
3:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-6.938896, -8, -6.962986, 1.0315533, -8, 1.0028238, -8, 8, 8, 8, 8, -8)

[sub_resource type="TileSet" id="TileSet_je4n3"]
physics_layer_0/collision_layer = 1
sources/0 = SubResource("TileSetAtlasSource_nn7wm")

[sub_resource type="AtlasTexture" id="AtlasTexture_2uve2"]
atlas = ExtResource("11_w6op7")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_dy8eh"]
atlas = ExtResource("11_w6op7")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ej2fq"]
atlas = ExtResource("11_w6op7")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_unyb1"]
atlas = ExtResource("11_w6op7")
region = Rect2(48, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_jddgd"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2uve2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dy8eh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ej2fq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_unyb1")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_056qb"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="AtlasTexture" id="AtlasTexture_jddgd"]
atlas = ExtResource("12_2uve2")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_t7dqo"]
atlas = ExtResource("12_2uve2")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_jojiu"]
atlas = ExtResource("12_2uve2")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_keibd"]
atlas = ExtResource("12_2uve2")
region = Rect2(48, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_q007j"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jddgd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t7dqo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jojiu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_keibd")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_jddgd"]
size = Vector2(134, 171)

[sub_resource type="WorldBoundaryShape2D" id="WorldBoundaryShape2D_jddgd"]

[sub_resource type="Animation" id="Animation_t7dqo"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0, 0, 0, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_jddgd"]
resource_name = "new_animation"
length = 3.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0, 0, 0, 1), Color(0, 0, 0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.5, 1, 2.5, 3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_t7dqo"]
_data = {
&"RESET": SubResource("Animation_t7dqo"),
&"new_animation": SubResource("Animation_jddgd")
}

[node name="chapter 1" type="Node2D"]

[node name="Parallax2D" type="Parallax2D" parent="."]
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_je4n3")

[node name="Parallax2D4" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.1, 0.1)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D4"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_stnnl")

[node name="Parallax2D3" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.3, 0.3)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D3"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_id86x")

[node name="Parallax2D2" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.6, 0.6)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D2"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("2_r1lq4")

[node name="npc2" parent="." instance=ExtResource("10_pdrt4")]
position = Vector2(835, 540)
dialogue = Array[String](["Jump while on a wall to wall climb."])

[node name="CollisionShape2D" parent="npc2" index="0"]
position = Vector2(-19.75, 0)

[node name="Sprite2D" parent="npc2" index="1"]
position = Vector2(0, 5)
sprite_frames = SubResource("SpriteFrames_ej2fq")

[node name="Label" parent="npc2" index="2"]
anchors_preset = 5
anchor_top = 0.0
anchor_bottom = 0.0
offset_top = -105.0
offset_bottom = -57.0
grow_vertical = 1

[node name="npc3" parent="." instance=ExtResource("10_pdrt4")]
position = Vector2(2441, 349)
dialogue = Array[String](["Jump twice to perform a double jump."])

[node name="CollisionShape2D" parent="npc3" index="0"]
position = Vector2(-0.5, 0)
shape = SubResource("RectangleShape2D_dy8eh")

[node name="Sprite2D" parent="npc3" index="1"]
position = Vector2(0, 5)
sprite_frames = SubResource("SpriteFrames_ej2fq")

[node name="Label" parent="npc3" index="2"]
anchors_preset = 5
anchor_top = 0.0
anchor_bottom = 0.0
offset_top = -105.0
offset_bottom = -57.0
grow_vertical = 1

[node name="npc4" parent="." instance=ExtResource("10_pdrt4")]
position = Vector2(3219, 156)
dialogue = Array[String](["Hold shift to dash."])

[node name="CollisionShape2D" parent="npc4" index="0"]
position = Vector2(-19.75, 0)

[node name="Sprite2D" parent="npc4" index="1"]
position = Vector2(0, 5)
sprite_frames = SubResource("SpriteFrames_ej2fq")

[node name="Label" parent="npc4" index="2"]
anchors_preset = 5
anchor_top = 0.0
anchor_bottom = 0.0
offset_top = -105.0
offset_bottom = -57.0
grow_vertical = 1

[node name="Factory" type="Sprite2D" parent="."]
position = Vector2(9643.75, -348.2502)
scale = Vector2(7.390627, 7.390627)
texture = ExtResource("15_unyb1")

[node name="TileMapLayer3" type="TileMapLayer" parent="."]
scale = Vector2(6, 6)
tile_map_data = PackedByteArray("AAAKAAMAAAAAAAEAAAAKAAQAAAAAAAIAAAALAAMAAAABAAEAAAALAAQAAAABAAIAAAAKAAUAAAAAAAIAAAAKAAYAAAAAAAIAAAAKAAcAAAAAAAIAAAAKAAgAAAAAAAIAAAAKAAkAAAAAAAIAAAAKAAoAAAAAAAIAAAALAAUAAAABAAIAAAALAAYAAAABAAIAAAALAAcAAAABAAIAAAALAAgAAAABAAIAAAALAAkAAAABAAIAAAALAAoAAAABAAIAAAAMAAUAAAABAAIAAAAMAAYAAAABAAIAAAAMAAcAAAABAAIAAAAMAAgAAAABAAIAAAAMAAkAAAABAAIAAAAMAAoAAAABAAIAAAANAAUAAAABAAIAAAANAAYAAAABAAIAAAANAAcAAAABAAIAAAANAAgAAAABAAIAAAANAAkAAAABAAIAAAANAAoAAAABAAIAAAAOAAUAAAAAAAIAABAOAAYAAAAAAAIAABAOAAcAAAAAAAIAABAOAAgAAAAAAAIAABAOAAkAAAAAAAIAABAOAAoAAAAAAAIAABAMAAMAAAABAAEAAAANAAMAAAABAAEAAAAOAAMAAAAAAAEAABAOAAQAAAAAAAIAABAMAAQAAAABAAIAAAANAAQAAAABAAIAAAAuAAIAAAAAAAEAAAAuAAMAAAAAAAIAAAAuAAQAAAAAAAIAAAAuAAUAAAAAAAIAAAAuAAYAAAAAAAIAAAAuAAcAAAAAAAIAAAAuAAgAAAAAAAIAAAAuAAkAAAAAAAIAAAAvAAIAAAABAAEAAAAvAAMAAAABAAIAAAAvAAQAAAABAAIAAAAvAAUAAAABAAIAAAAvAAYAAAABAAIAAAAvAAcAAAABAAIAAAAvAAgAAAABAAIAAAAvAAkAAAABAAIAAAAwAAIAAAABAAEAAAAwAAMAAAABAAIAAAAwAAQAAAABAAIAAAAwAAUAAAABAAIAAAAwAAYAAAABAAIAAAAwAAcAAAABAAIAAAAwAAgAAAABAAIAAAAwAAkAAAABAAIAAAAxAAIAAAABAAEAAAAxAAMAAAABAAIAAAAxAAQAAAABAAIAAAAxAAUAAAABAAIAAAAxAAYAAAABAAIAAAAxAAcAAAABAAIAAAAxAAgAAAABAAIAAAAxAAkAAAABAAIAAAAyAAIAAAAAAAEAABAyAAMAAAAAAAIAABAyAAQAAAAAAAIAABAyAAUAAAAAAAIAABAyAAYAAAAAAAIAABAyAAcAAAAAAAIAABAyAAgAAAAAAAIAABAyAAkAAAAAAAIAABAvAP//AAAAAAMAAAAvAAAAAAAAAAQAAAAvAAEAAAAAAAUAAAA=")
tile_set = SubResource("TileSet_je4n3")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
scale = Vector2(6, 6)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_je4n3")

[node name="player" parent="." instance=ExtResource("2_nn7wm")]
position = Vector2(588, 384)

[node name="npc" parent="." node_paths=PackedStringArray("multiple_labels") instance=ExtResource("10_pdrt4")]
position = Vector2(5122, 342)
dialogue = Array[String](["0:And that is why I always brew my coffee before brushing my teeth!", "0:Oh! Hello there stranger, who are you?", "p:I'm a recruiter. I'm looking for someone named Lim Wei Jen. Do you know him?", "0:That name does ring a bell.", "1:Yea, that's the guy that invents random stuff every day right?", "0:You're giving him credit by calling it inventions.", "0:I heard he built a whole game world... just to test a single jump button!", "1:Shut up. I once heard that he beat a computer in chess, by unplugging it.", "0:They say he freelances online. I don't know what that means... is it like selling fish, but with pixels?", "0:Why do you ask anyway, fellow recruiter?", "p:Well, I am here to review his portfolio and hopefully meet him in person.", "1:I see... well sometimes you might meet him here, playing badminton with us, or engaging in voluntary works.", "0:Yea, but most of the time he is  in his lab crafting who knows what. ", "1:His lab is on the far right of this village. If you're lucky, you might meet him there.", "1:But be warned, he's set up a few... obstacle courses, shall we say.", "0:Yep, he's a dreamer, that one. Always building little worlds for people to explore."])
multiple_labels = [NodePath("Label"), NodePath("Label2")]

[node name="CollisionShape2D" parent="npc" index="0"]
position = Vector2(125.5, 0)

[node name="Sprite2D" parent="npc" index="1"]
sprite_frames = SubResource("SpriteFrames_jddgd")
autoplay = "default"

[node name="Label" parent="npc" index="2"]
offset_left = -12.0
offset_top = -129.0
offset_right = 11.0
offset_bottom = -81.0

[node name="Label2" type="Label" parent="npc"]
z_index = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 239.0
offset_top = -127.0
offset_right = 260.0
offset_bottom = -79.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("10_ej2fq")
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_056qb")
horizontal_alignment = 1

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="npc"]
position = Vector2(249, 0)
scale = Vector2(5.25, 5.25)
sprite_frames = SubResource("SpriteFrames_q007j")
autoplay = "default"

[node name="TileMapLayer2" type="TileMapLayer" parent="."]
scale = Vector2(6, 6)
tile_map_data = PackedByteArray("AAAIAAUAAAAEAAEAAAARAAMAAAACAAEAAAAUAAMAAAACAAEAAAAXAAMAAAACAAEAAAAYAAMAAAADAAEAAAAnAAEAAAADAAEAAAAwAAEAAAADAAEAAAAzAAMAAAADAAEAAAA6AAMAAAADAAEAAAA+AAMAAAAEAAEAAAA3AAMAAAAEAAEAAAA4AAMAAAACAAEAAAAxAAEAAAACAAEAAAA5AAMAAAADAAAAAAAoAAEAAAADAAAAAAAdAAIAAAADAAAAAAANAAIAAAADAAAAAAALAAIAAAADAAAAAAAMAAIAAAAEAAAAAAAcAAIAAAAEAAAAAAAyAAMAAAACAAEAAABFAAMAAAACAAEAAABHAAMAAAACAAEAAABNAAEAAAADAAEAAABMAAEAAAADAAEAAABOAAEAAAAEAAEAAABLAAEAAAAEAAAAAABSAAMAAAAEAAAAAABUAAMAAAAEAAEAAABTAAMAAAACAAEAAABVAAMAAAACAAEAAABIAAMAAAADAAEAAAA=")
tile_set = SubResource("TileSet_je4n3")

[node name="Parallax2D6" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.27058825)
scroll_scale = Vector2(1.1, 1.1)
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D6"]
position = Vector2(586.3334, 271)
scale = Vector2(10, 10)
texture = ExtResource("9_wkftq")

[node name="Parallax2D5" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.91764706)
scroll_scale = Vector2(1.2, 1.2)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D5"]
position = Vector2(586.3334, 271)
scale = Vector2(10, 10)
texture = ExtResource("6_2fp7s")

[node name="Sprite2D2" type="Sprite2D" parent="Parallax2D5"]
position = Vector2(586.3334, 1231)
scale = Vector2(10, 10)
texture = ExtResource("7_qmn82")

[node name="next level teleporter" parent="." instance=ExtResource("16_jddgd")]
position = Vector2(9404, -50)
next_level_scene = ExtResource("17_t7dqo")

[node name="CollisionShape2D" parent="next level teleporter" index="0"]
position = Vector2(-4, -36.5)
shape = SubResource("RectangleShape2D_jddgd")

[node name="wall" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="wall"]
position = Vector2(86, 393)
rotation = 1.5707964
shape = SubResource("WorldBoundaryShape2D_jddgd")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="wall"]
position = Vector2(9930, -132)
rotation = -1.5707964
shape = SubResource("WorldBoundaryShape2D_jddgd")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="CanvasLayer"]
libraries = {
&"": SubResource("AnimationLibrary_t7dqo")
}
autoplay = "new_animation"

[node name="Label" type="Label" parent="CanvasLayer"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -261.0
offset_top = -141.5
offset_right = 261.0
offset_bottom = 141.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("10_ej2fq")
theme_override_font_sizes/font_size = 100
text = "Chapter 1:
About Me"
horizontal_alignment = 1

[editable path="npc2"]
[editable path="npc3"]
[editable path="npc4"]
[editable path="npc"]
[editable path="next level teleporter"]
