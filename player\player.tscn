[gd_scene load_steps=34 format=3 uid="uid://fdb8t12ej0n1"]

[ext_resource type="Script" uid="uid://bxc4aq81utiig" path="res://player/player.gd" id="1_rkbax"]
[ext_resource type="Texture2D" uid="uid://q8ci44qf6ahc" path="res://player/sprites/Player Idle.png" id="2_rkbax"]
[ext_resource type="Texture2D" uid="uid://dgk3c3lkodcqm" path="res://player/sprites/Player Jump.png" id="3_g1dw6"]
[ext_resource type="Texture2D" uid="uid://b31jtwvco7en" path="res://player/sprites/Player Double Jump.png" id="3_yw30f"]
[ext_resource type="Texture2D" uid="uid://chc6n0uaca1ei" path="res://player/sprites/Player Fall.png" id="4_qjkh3"]
[ext_resource type="Texture2D" uid="uid://dugrvjxruwbau" path="res://player/sprites/Player Walk.png" id="6_g6k8r"]
[ext_resource type="Texture2D" uid="uid://b3a5rhtslupih" path="res://player/sprites/Player Wall Slide.png" id="7_boad6"]
[ext_resource type="FontFile" uid="uid://231c8iuwhd1b" path="res://Poppins/Poppins-Black.ttf" id="8_rgyib"]
[ext_resource type="AudioStream" uid="uid://kvqu2wjjnvbq" path="res://player/sound effects/sand-walk-106366.mp3" id="9_hg6s5"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_kqu6x"]
size = Vector2(42, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_rgyib"]
atlas = ExtResource("3_yw30f")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_hg6s5"]
atlas = ExtResource("3_yw30f")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_8t03j"]
atlas = ExtResource("3_yw30f")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_2ieo8"]
atlas = ExtResource("3_yw30f")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ebec5"]
atlas = ExtResource("3_yw30f")
region = Rect2(64, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_yllr7"]
atlas = ExtResource("3_yw30f")
region = Rect2(80, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_kb6p2"]
atlas = ExtResource("3_yw30f")
region = Rect2(96, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_g1dw6"]
atlas = ExtResource("2_rkbax")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_yw30f"]
atlas = ExtResource("2_rkbax")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_qjkh3"]
atlas = ExtResource("2_rkbax")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_g6k8r"]
atlas = ExtResource("2_rkbax")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_boad6"]
atlas = ExtResource("3_g1dw6")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_32hag"]
atlas = ExtResource("6_g6k8r")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_tqiix"]
atlas = ExtResource("6_g6k8r")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_e7oew"]
atlas = ExtResource("6_g6k8r")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_c35mf"]
atlas = ExtResource("6_g6k8r")
region = Rect2(64, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_65viv"]
atlas = ExtResource("6_g6k8r")
region = Rect2(80, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_wodsf"]
atlas = ExtResource("6_g6k8r")
region = Rect2(0, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_oul6g"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_rgyib")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hg6s5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8t03j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2ieo8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ebec5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yllr7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kb6p2")
}],
"loop": false,
"name": &"double_jump",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("4_qjkh3")
}],
"loop": true,
"name": &"fall",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_g1dw6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yw30f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qjkh3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g6k8r")
}],
"loop": true,
"name": &"idle",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_boad6")
}],
"loop": false,
"name": &"jump",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_32hag")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tqiix")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e7oew")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c35mf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_65viv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wodsf")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("7_boad6")
}],
"loop": true,
"name": &"wall_slide",
"speed": 5.0
}]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rgyib"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="Animation" id="Animation_hg6s5"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("interact label:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("interact label:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-18.5, -108)]
}

[sub_resource type="Animation" id="Animation_rgyib"]
resource_name = "show interact sign"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("interact label:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("interact label:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-18.5, -108), Vector2(-18.5, -117)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_hg6s5"]
_data = {
&"RESET": SubResource("Animation_hg6s5"),
&"show interact sign": SubResource("Animation_rgyib")
}

[node name="player" type="CharacterBody2D"]
script = ExtResource("1_rkbax")
acceleration = 5000.0
friction = 2000.0
jump_cut_multiplier = 0.1
max_fall_speed = 1000.0
fast_fall_multiplier = 4.0
dash_speed = 1000.0
wall_jump_velocity = Vector2(100, -500)
wall_jump_push_time = 0.1

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_kqu6x")

[node name="Sprite2D" type="AnimatedSprite2D" parent="."]
scale = Vector2(5.25, 5.25)
sprite_frames = SubResource("SpriteFrames_oul6g")
animation = &"walk"
autoplay = "idle"

[node name="Camera2D" type="Camera2D" parent="."]
position_smoothing_enabled = true
drag_horizontal_enabled = true
drag_vertical_enabled = true
drag_left_margin = 0.1
drag_right_margin = 0.1

[node name="interact label" type="Label" parent="."]
modulate = Color(1, 1, 1, 0)
z_index = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -18.5
offset_top = -108.0
offset_right = 18.5
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("8_rgyib")
theme_override_font_sizes/font_size = 30
theme_override_styles/normal = SubResource("StyleBoxFlat_rgyib")
text = "E"
horizontal_alignment = 1

[node name="Label" type="Label" parent="."]
visible = false
z_index = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.5
offset_top = -126.0
offset_right = 10.5
offset_bottom = -78.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("8_rgyib")
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_rgyib")
horizontal_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_hg6s5")
}

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("9_hg6s5")
autoplay = true
