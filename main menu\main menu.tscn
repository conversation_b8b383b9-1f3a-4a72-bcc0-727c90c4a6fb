[gd_scene load_steps=20 format=3 uid="uid://dtree0r0c10uf"]

[ext_resource type="Texture2D" uid="uid://ipb311v4n477" path="res://chapters/chapter 1/environment sprites/background.png" id="1_5bngd"]
[ext_resource type="Script" uid="uid://co13r6nkbtgmb" path="res://main menu/main_menu.gd" id="1_vltye"]
[ext_resource type="Texture2D" uid="uid://bw66tbhphpctr" path="res://chapters/chapter 1/environment sprites/background 4.png" id="2_vltye"]
[ext_resource type="Texture2D" uid="uid://bfk28a3pf8ply" path="res://chapters/chapter 1/environment sprites/background 3.png" id="3_uef0c"]
[ext_resource type="Texture2D" uid="uid://c2eq61hgax2ss" path="res://chapters/chapter 1/environment sprites/background 2.png" id="4_8s3e4"]
[ext_resource type="Texture2D" uid="uid://dp2g0wix1wrb2" path="res://chapters/chapter 1/environment sprites/background 7.png" id="6_1m6bl"]
[ext_resource type="Texture2D" uid="uid://dnmnkyaqcx2me" path="res://chapters/chapter 1/environment sprites/background 5.png" id="7_bswgn"]
[ext_resource type="FontFile" uid="uid://231c8iuwhd1b" path="res://Poppins/Poppins-Black.ttf" id="7_o30qs"]
[ext_resource type="Texture2D" uid="uid://cwa54gjt06k5k" path="res://chapters/chapter 1/environment sprites/background 6.png" id="8_o30qs"]
[ext_resource type="Texture2D" uid="uid://dkp4bjwtluev6" path="res://chapters/chapter 4/contact/contact board.png" id="10_kv1ht"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bswgn"]
bg_color = Color(0.6, 0.6, 0.6, 0)
border_width_left = 10
border_width_top = 10
border_width_right = 10
border_width_bottom = 10
border_color = Color(1, 1, 1, 1)
corner_radius_top_left = 20
corner_radius_top_right = 20
corner_radius_bottom_right = 20
corner_radius_bottom_left = 20
expand_margin_left = 10.0
expand_margin_top = 10.0
expand_margin_right = 10.0
expand_margin_bottom = 10.0

[sub_resource type="Animation" id="Animation_bswgn"]
resource_name = "mouse in"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Button:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.3, 1.3)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Button/PanelContainer:scale")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1e-05, 1), Vector2(1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Button:rotation")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, -0.08726646]
}

[sub_resource type="Animation" id="Animation_o30qs"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Button:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Button/PanelContainer:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1e-05, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Button:rotation")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Button:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_noelm"]
resource_name = "show button"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Button:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_o30qs"]
_data = {
&"RESET": SubResource("Animation_o30qs"),
&"mouse in": SubResource("Animation_bswgn"),
&"show button": SubResource("Animation_noelm")
}

[sub_resource type="Animation" id="Animation_bse43"]
resource_name = "new_animation"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_18t1u"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("PanelContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Control:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Control:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.2, 0.2)]
}

[sub_resource type="Animation" id="Animation_im45v"]
resource_name = "show notice"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PanelContainer:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Control:scale")
tracks/2/interp = 2
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.41, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.2, 0.2), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_18t1u"]
_data = {
&"RESET": SubResource("Animation_18t1u"),
&"new_animation": SubResource("Animation_bse43"),
&"show notice": SubResource("Animation_im45v")
}

[node name="main menu" type="Node2D"]
script = ExtResource("1_vltye")

[node name="Parallax2D" type="Parallax2D" parent="."]
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("1_5bngd")

[node name="Parallax2D4" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.1, 0.1)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D4"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("2_vltye")

[node name="Parallax2D3" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.3, 0.3)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D3"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_uef0c")

[node name="Parallax2D2" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.6, 0.6)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D2"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("4_8s3e4")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="Parallax2D6" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.27058825)
scroll_scale = Vector2(1.1, 1.1)
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D6"]
position = Vector2(586.3334, 271)
scale = Vector2(10, 10)
texture = ExtResource("6_1m6bl")

[node name="Parallax2D7" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.9098039)

[node name="Label" type="Label" parent="Parallax2D7"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -499.0
offset_top = -144.0
offset_right = 404.0
offset_bottom = 67.0
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 150
horizontal_alignment = 1

[node name="Parallax2D8" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.41960785)
scroll_scale = Vector2(1.005, 1.005)

[node name="Label" type="Label" parent="Parallax2D8"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -499.0
offset_top = -144.0
offset_right = 404.0
offset_bottom = 67.0
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 150
horizontal_alignment = 1

[node name="Parallax2D9" type="Parallax2D" parent="."]
scroll_offset = Vector2(0, 20)

[node name="Button" type="Button" parent="Parallax2D9"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -192.0
offset_top = 107.0
offset_right = 63.0
offset_bottom = 227.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(121, 62)
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 80
text = "BEGIN"
flat = true

[node name="PanelContainer" type="PanelContainer" parent="Parallax2D9/Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(1e-05, 1)
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_bswgn")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Parallax2D9"]
libraries = {
&"": SubResource("AnimationLibrary_o30qs")
}

[node name="Parallax2D5" type="Parallax2D" parent="."]
modulate = Color(1, 1, 1, 0.91764706)
scroll_scale = Vector2(1.2, 1.2)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D5"]
position = Vector2(586.3334, 271)
scale = Vector2(10, 10)
texture = ExtResource("7_bswgn")

[node name="Sprite2D2" type="Sprite2D" parent="Parallax2D5"]
position = Vector2(586.3334, 1231)
scale = Vector2(10, 10)
texture = ExtResource("8_o30qs")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="PanelContainer" type="PanelContainer" parent="CanvasLayer"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Control" type="Control" parent="CanvasLayer"]
modulate = Color(1, 1, 1, 0)
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.2, 0.2)

[node name="TextureRect" type="TextureRect" parent="CanvasLayer/Control"]
modulate = Color(0.8819366, 0.8819366, 0.88193655, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -437.33334
offset_top = -328.0
offset_right = 437.3334
offset_bottom = 328.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("10_kv1ht")
stretch_mode = 5

[node name="Label" type="Label" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -345.0
offset_top = -136.5
offset_right = 345.0
offset_bottom = 136.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 30
text = "This is an immersive and interactive portfolio experience, designed to be explored in 3-4 minutes.
Prefer something quicker? A text version is available.
Otherwise... let's begin!"
horizontal_alignment = 1
autowrap_mode = 2

[node name="Label2" type="Label" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -258.5
offset_top = -257.0
offset_right = 258.5
offset_bottom = -56.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 60
text = "Welcome!"
horizontal_alignment = 1

[node name="ready button" type="Button" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -81.5
offset_top = 190.0
offset_right = 81.5
offset_bottom = 241.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 40
text = "I'm Ready"
flat = true

[node name="text version button" type="Button" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 108.5
offset_top = -2.0
offset_right = 306.5
offset_bottom = 49.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("7_o30qs")
theme_override_font_sizes/font_size = 30
text = "text version"
flat = true

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(0, 0, 0, 1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="CanvasLayer"]
libraries = {
&"": SubResource("AnimationLibrary_18t1u")
}
autoplay = "new_animation"

[connection signal="mouse_entered" from="Parallax2D9/Button" to="." method="_on_button_mouse_entered"]
[connection signal="mouse_exited" from="Parallax2D9/Button" to="." method="_on_button_mouse_exited"]
[connection signal="pressed" from="Parallax2D9/Button" to="." method="_on_button_pressed"]
[connection signal="pressed" from="CanvasLayer/Control/ready button" to="." method="_on_ready_button_pressed"]
