[gd_scene load_steps=42 format=4 uid="uid://ce88y1pevtcre"]

[ext_resource type="PackedScene" uid="uid://fdb8t12ej0n1" path="res://player/player.tscn" id="1_52ogr"]
[ext_resource type="Texture2D" uid="uid://dak26hogvc2l4" path="res://chapters/chapter 4/environment sprites/tilemap.png" id="2_o1ef6"]
[ext_resource type="Texture2D" uid="uid://ipb311v4n477" path="res://chapters/chapter 1/environment sprites/background.png" id="3_pqpbe"]
[ext_resource type="Texture2D" uid="uid://bw66tbhphpctr" path="res://chapters/chapter 1/environment sprites/background 4.png" id="4_s0d6c"]
[ext_resource type="Texture2D" uid="uid://bfk28a3pf8ply" path="res://chapters/chapter 1/environment sprites/background 3.png" id="5_30b5l"]
[ext_resource type="Texture2D" uid="uid://c2eq61hgax2ss" path="res://chapters/chapter 1/environment sprites/background 2.png" id="6_xnb2o"]
[ext_resource type="Texture2D" uid="uid://lba5wteqdmln" path="res://chapters/chapter 4/environment sprites/computer.png" id="7_s0d6c"]
[ext_resource type="PackedScene" uid="uid://bxx41bcyxworf" path="res://NPC/npc.tscn" id="8_30b5l"]
[ext_resource type="Texture2D" uid="uid://dbftgmsxnxgvr" path="res://chapters/chapter 4/npc sprites/lim wei jen contact.png" id="9_kexvq"]
[ext_resource type="Texture2D" uid="uid://38nbfmibav2s" path="res://chapters/chapter 4/npc sprites/lim wei jen typing.png" id="9_xnb2o"]
[ext_resource type="Texture2D" uid="uid://bvmiepxs86uw0" path="res://chapters/chapter 4/npc sprites/lim wei jen contact idle.png" id="10_58hyh"]
[ext_resource type="Texture2D" uid="uid://cm6xc6rectn8b" path="res://chapters/chapter 4/npc sprites/lim wei jen spinning.png" id="10_wp12d"]
[ext_resource type="Texture2D" uid="uid://dwqw33xowp67x" path="res://chapters/chapter 4/npc sprites/lim wei jen idle.png" id="12_t45py"]
[ext_resource type="FontFile" uid="uid://231c8iuwhd1b" path="res://Poppins/Poppins-Black.ttf" id="14_r3vtx"]
[ext_resource type="PackedScene" uid="uid://bvrcgyym7ot4y" path="res://chapters/chapter 4/contact/contact.tscn" id="15_jrbs3"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_l56qg"]
texture = ExtResource("2_o1ef6")
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:0/0 = 0
1:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:1/0 = 0
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:0/0 = 0
3:0/0 = 0
3:1/0 = 0
4:1/0 = 0
2:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
4:3/0 = 0
3:3/0 = 0
1:5/0 = 0
3:5/0 = 0

[sub_resource type="TileSet" id="TileSet_52ogr"]
physics_layer_0/collision_layer = 1
sources/0 = SubResource("TileSetAtlasSource_l56qg")

[sub_resource type="AtlasTexture" id="AtlasTexture_lk4v3"]
atlas = ExtResource("9_kexvq")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_mpsfm"]
atlas = ExtResource("9_kexvq")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_eopo7"]
atlas = ExtResource("9_kexvq")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_snssd"]
atlas = ExtResource("10_58hyh")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_b0o3p"]
atlas = ExtResource("10_58hyh")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_1t2na"]
atlas = ExtResource("10_58hyh")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_edy2g"]
atlas = ExtResource("10_58hyh")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_wp12d"]
atlas = ExtResource("9_xnb2o")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_kexvq"]
atlas = ExtResource("9_xnb2o")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_58hyh"]
atlas = ExtResource("9_xnb2o")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_t45py"]
atlas = ExtResource("9_xnb2o")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ba3pl"]
atlas = ExtResource("12_t45py")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_kcvlw"]
atlas = ExtResource("12_t45py")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_entyi"]
atlas = ExtResource("12_t45py")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_3worr"]
atlas = ExtResource("12_t45py")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_r3vtx"]
atlas = ExtResource("10_wp12d")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_jrbs3"]
atlas = ExtResource("10_wp12d")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_2vq4h"]
atlas = ExtResource("10_wp12d")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_l8euw"]
atlas = ExtResource("10_wp12d")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_hnwno"]
atlas = ExtResource("10_wp12d")
region = Rect2(64, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_30b5l"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_lk4v3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lk4v3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mpsfm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mpsfm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mpsfm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eopo7")
}],
"loop": false,
"name": &"contact",
"speed": 2.5
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_snssd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b0o3p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1t2na")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_edy2g")
}],
"loop": true,
"name": &"contactidle",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kexvq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_58hyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t45py")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_58hyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t45py")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_58hyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kexvq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wp12d")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ba3pl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kcvlw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_entyi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3worr")
}],
"loop": true,
"name": &"idle",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_r3vtx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r3vtx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r3vtx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r3vtx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jrbs3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2vq4h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l8euw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnwno")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnwno")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnwno")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnwno")
}],
"loop": false,
"name": &"turn",
"speed": 5.0
}]

[sub_resource type="Animation" id="Animation_t7dqo"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0, 0, 0, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_jddgd"]
resource_name = "new_animation"
length = 3.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0, 0, 0, 1), Color(0, 0, 0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.5, 1, 2.5, 3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_t7dqo"]
_data = {
&"RESET": SubResource("Animation_t7dqo"),
&"new_animation": SubResource("Animation_jddgd")
}

[node name="chapter 4" type="Node2D"]

[node name="Parallax2D" type="Parallax2D" parent="."]
modulate = Color(0.34781694, 0.34781697, 0.3478169, 1)
z_index = -2
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_pqpbe")

[node name="Parallax2D4" type="Parallax2D" parent="."]
modulate = Color(0.34781694, 0.34781697, 0.3478169, 1)
z_index = -2
scroll_scale = Vector2(0.1, 0.1)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D4"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("4_s0d6c")

[node name="Parallax2D3" type="Parallax2D" parent="."]
modulate = Color(0.34781694, 0.34781697, 0.3478169, 1)
z_index = -2
scroll_scale = Vector2(0.3, 0.3)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D3"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("5_30b5l")

[node name="Parallax2D2" type="Parallax2D" parent="."]
modulate = Color(0.34781694, 0.34781697, 0.3478169, 1)
z_index = -2
scroll_scale = Vector2(0.6, 0.6)
repeat_size = Vector2(1280, 0)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D2"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("6_xnb2o")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
scale = Vector2(5.25, 5.25)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_52ogr")

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(2126, -348)
scale = Vector2(6, 6)
texture = ExtResource("7_s0d6c")

[node name="player" parent="." instance=ExtResource("1_52ogr")]
position = Vector2(-9, 47)

[node name="npc" parent="." instance=ExtResource("8_30b5l")]
position = Vector2(2101, -294)
dialogue = Array[String](["anim:turn", "anim:idle", "0:Oh! You actually made it all the way here.", "p:So you're... Lim Wei Jen?", "0:Yep, that's me. ", "0:Thank you for walking through this little world I built. It means a lot to me that you took the time to explore my story.", "anim:contact", "anim:contactidle", "0:As a token of gratitude, here's the real treasure you came for - my contact info.", "0:Feel free to reach out. Who knows, maybe the next world I create will be with you. ^_^", "contact"])

[node name="Sprite2D" parent="npc" index="1"]
sprite_frames = SubResource("SpriteFrames_30b5l")
animation = &"idle"
autoplay = "default"

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(0, 0, 0, 1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="CanvasLayer"]
libraries = {
&"": SubResource("AnimationLibrary_t7dqo")
}
autoplay = "new_animation"

[node name="Label" type="Label" parent="CanvasLayer"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -261.0
offset_top = -141.5
offset_right = 261.0
offset_bottom = 141.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("14_r3vtx")
theme_override_font_sizes/font_size = 100
text = "Chapter 4:
The New Beginning"
horizontal_alignment = 1

[node name="contact" parent="." instance=ExtResource("15_jrbs3")]

[editable path="npc"]
