[gd_scene load_steps=6 format=3 uid="uid://cxog5psgyyt40"]

[ext_resource type="Script" uid="uid://b67r1a8jwn5yr" path="res://next level teleporter/next_level_teleporter.gd" id="1_ofvfq"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_xnqe6"]
size = Vector2(134, 171)

[sub_resource type="Animation" id="Animation_ofvfq"]
resource_name = "fade in"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0, 0, 0, 0), Color(0, 0, 0, 1)]
}

[sub_resource type="Animation" id="Animation_fimfs"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0, 0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_fimfs"]
_data = {
&"RESET": SubResource("Animation_fimfs"),
&"fade in": SubResource("Animation_ofvfq")
}

[node name="next level teleporter" type="Area2D"]
script = ExtResource("1_ofvfq")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_xnqe6")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_fimfs")
}

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
