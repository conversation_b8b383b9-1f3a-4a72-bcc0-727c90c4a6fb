[gd_scene load_steps=85 format=4 uid="uid://c84s6iru81fqd"]

[ext_resource type="Texture2D" uid="uid://bsbi533o7a61h" path="res://chapters/chapter 2/environment sprites/background.png" id="1_dwed3"]
[ext_resource type="PackedScene" uid="uid://fdb8t12ej0n1" path="res://player/player.tscn" id="1_kn1i3"]
[ext_resource type="Texture2D" uid="uid://bjylhom4oxrdh" path="res://chapters/chapter 2/environment sprites/tilemap.png" id="1_rk2dk"]
[ext_resource type="Texture2D" uid="uid://cntweffmyicpq" path="res://chapters/chapter 2/environment sprites/background 1.png" id="2_0rnry"]
[ext_resource type="Texture2D" uid="uid://c81xkplfk0dal" path="res://chapters/chapter 2/environment sprites/background 2.png" id="3_l534y"]
[ext_resource type="Texture2D" uid="uid://drujk4s4rloax" path="res://chapters/chapter 2/npc sprites/github/github awake.png" id="5_ggqax"]
[ext_resource type="PackedScene" uid="uid://bxx41bcyxworf" path="res://NPC/npc.tscn" id="6_gaqhi"]
[ext_resource type="Texture2D" uid="uid://17diucbt2cpp" path="res://chapters/chapter 2/npc sprites/github/github idle.png" id="7_cbamf"]
[ext_resource type="Texture2D" uid="uid://djwo852iyignl" path="res://chapters/chapter 2/npc sprites/github/github sleeping.png" id="7_uynys"]
[ext_resource type="Texture2D" uid="uid://rlxh1alq4trt" path="res://chapters/chapter 2/npc sprites/github/github smirk.png" id="8_3u58u"]
[ext_resource type="Texture2D" uid="uid://cgx5g44xi06wc" path="res://chapters/chapter 2/npc sprites/vscode/vscode.png" id="9_gxtk0"]
[ext_resource type="Texture2D" uid="uid://ddwwxvvq81ai2" path="res://chapters/chapter 2/npc sprites/nextjs.png" id="10_cun2x"]
[ext_resource type="Texture2D" uid="uid://ccuf5fgm2kixx" path="res://chapters/chapter 2/npc sprites/blender.png" id="11_o43xw"]
[ext_resource type="Texture2D" uid="uid://dpug3fdssbao2" path="res://chapters/chapter 2/npc sprites/godot/godot.png" id="12_wft16"]
[ext_resource type="Texture2D" uid="uid://byk5siswl0tyn" path="res://chapters/chapter 2/npc sprites/godot/godot sleeping.png" id="13_27lui"]
[ext_resource type="Texture2D" uid="uid://cqduwgyajlbva" path="res://chapters/chapter 2/npc sprites/python.png" id="14_03odw"]
[ext_resource type="Texture2D" uid="uid://brni5xgrqeqns" path="res://chapters/chapter 2/npc sprites/typescript.png" id="15_t4wku"]
[ext_resource type="FontFile" uid="uid://231c8iuwhd1b" path="res://Poppins/Poppins-Black.ttf" id="16_rflqm"]
[ext_resource type="Texture2D" uid="uid://cecyj3cyd2ol1" path="res://chapters/chapter 2/npc sprites/cpp.png" id="16_vd7fa"]
[ext_resource type="PackedScene" uid="uid://d2yexband52t1" path="res://chapters/chapter 2/employees not working/employees_not_working.tscn" id="20_lgq8u"]
[ext_resource type="PackedScene" uid="uid://cxog5psgyyt40" path="res://next level teleporter/next_level_teleporter.tscn" id="21_4hcmd"]
[ext_resource type="PackedScene" uid="uid://cvj0ukncyb5pa" path="res://chapters/chapter 3/chapter_3.tscn" id="22_o8wi0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_uynys"]
size = Vector2(90.25, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_cun2x"]
atlas = ExtResource("5_ggqax")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_o43xw"]
atlas = ExtResource("5_ggqax")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_wft16"]
atlas = ExtResource("5_ggqax")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_27lui"]
atlas = ExtResource("5_ggqax")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_03odw"]
atlas = ExtResource("5_ggqax")
region = Rect2(64, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ggqax"]
atlas = ExtResource("7_uynys")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_cbamf"]
atlas = ExtResource("7_uynys")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_3u58u"]
atlas = ExtResource("7_uynys")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_gxtk0"]
atlas = ExtResource("7_uynys")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_t4wku"]
atlas = ExtResource("7_cbamf")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_rflqm"]
atlas = ExtResource("7_cbamf")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_vd7fa"]
atlas = ExtResource("7_cbamf")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_lgq8u"]
atlas = ExtResource("7_cbamf")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_4hcmd"]
atlas = ExtResource("7_cbamf")
region = Rect2(64, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_o8wi0"]
atlas = ExtResource("7_cbamf")
region = Rect2(80, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_n8jlt"]
atlas = ExtResource("7_cbamf")
region = Rect2(96, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_vv6m2"]
atlas = ExtResource("7_cbamf")
region = Rect2(112, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_5rv1d"]
atlas = ExtResource("7_cbamf")
region = Rect2(128, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_nhlpk"]
atlas = ExtResource("7_cbamf")
region = Rect2(144, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_allwa"]
atlas = ExtResource("7_cbamf")
region = Rect2(160, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_85b78"]
atlas = ExtResource("8_3u58u")
region = Rect2(0, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_gaqhi"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cun2x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o43xw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wft16")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_27lui")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_27lui")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_27lui")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_03odw")
}],
"loop": false,
"name": &"awake",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ggqax")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cbamf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3u58u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gxtk0")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_t4wku")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rflqm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vd7fa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lgq8u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4hcmd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o8wi0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vv6m2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n8jlt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5rv1d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nhlpk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_allwa")
}],
"loop": true,
"name": &"idle",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_85b78")
}],
"loop": true,
"name": &"smirk",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_gxtk0"]
size = Vector2(267.125, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_0a1ft"]
atlas = ExtResource("9_gxtk0")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_yscaq"]
atlas = ExtResource("9_gxtk0")
region = Rect2(16, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_ksx6q"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0a1ft")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yscaq")
}],
"loop": true,
"name": &"default",
"speed": 2.5
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_cun2x"]
size = Vector2(162.78125, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_ksx6q"]
atlas = ExtResource("10_cun2x")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_dk8qt"]
atlas = ExtResource("10_cun2x")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_jw42e"]
atlas = ExtResource("10_cun2x")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_6xy6v"]
atlas = ExtResource("10_cun2x")
region = Rect2(48, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_jkbbk"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ksx6q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dk8qt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jw42e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6xy6v")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_jkbbk"]
atlas = ExtResource("11_o43xw")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_bodqh"]
atlas = ExtResource("11_o43xw")
region = Rect2(16, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_cun2x"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jkbbk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bodqh")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_27lui"]
size = Vector2(253.39063, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_j25re"]
atlas = ExtResource("12_wft16")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_hnbn3"]
atlas = ExtResource("12_wft16")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_6m50s"]
atlas = ExtResource("12_wft16")
region = Rect2(32, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_6k33i"]
atlas = ExtResource("12_wft16")
region = Rect2(48, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ok2l6"]
atlas = ExtResource("12_wft16")
region = Rect2(64, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ynboe"]
atlas = ExtResource("12_wft16")
region = Rect2(80, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_cirby"]
atlas = ExtResource("12_wft16")
region = Rect2(96, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_n627n"]
atlas = ExtResource("12_wft16")
region = Rect2(112, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_8qvkt"]
atlas = ExtResource("13_27lui")
region = Rect2(0, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_o43xw"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_j25re")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnbn3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6m50s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6k33i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ok2l6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ynboe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cirby")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n627n")
}],
"loop": true,
"name": &"default",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_8qvkt")
}],
"loop": true,
"name": &"new_animation",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_t4wku"]
size = Vector2(406.6953, 72)

[sub_resource type="AtlasTexture" id="AtlasTexture_eie1s"]
atlas = ExtResource("14_03odw")
region = Rect2(0, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_o1j8l"]
atlas = ExtResource("14_03odw")
region = Rect2(16, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_27lui"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_eie1s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o1j8l")
}],
"loop": true,
"name": &"default",
"speed": 2.5
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_68p13"]
atlas = ExtResource("15_t4wku")
region = Rect2(16, 0, 16, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_7n6ao"]
atlas = ExtResource("15_t4wku")
region = Rect2(0, 0, 16, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_1q64l"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_68p13")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7n6ao")
}],
"loop": true,
"name": &"default",
"speed": 2.5
}]

[sub_resource type="SpriteFrames" id="SpriteFrames_rflqm"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("16_vd7fa")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_056qb"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_dwed3"]
texture = ExtResource("1_rk2dk")
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:0/0 = 0
1:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -4.0112953, 8, -4.011296, 8, -8, -8, -8)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -4.0112953, 8, -4.011296, 8, -8, -8, -8)
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:1/0 = 0
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
0:2/0 = 0
0:3/0 = 0
5:1/0 = 0
4:0/0 = 0
5:0/0 = 0
1:2/0 = 0
1:3/0 = 0
1:4/0 = 0

[sub_resource type="TileSet" id="TileSet_kn1i3"]
physics_layer_0/collision_layer = 1
sources/0 = SubResource("TileSetAtlasSource_dwed3")

[sub_resource type="RectangleShape2D" id="RectangleShape2D_o8wi0"]
size = Vector2(83, 219)

[sub_resource type="Animation" id="Animation_t7dqo"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0, 0, 0, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_jddgd"]
resource_name = "new_animation"
length = 3.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0, 0, 0, 1), Color(0, 0, 0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Label:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.5, 1, 2.5, 3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_t7dqo"]
_data = {
&"RESET": SubResource("Animation_t7dqo"),
&"new_animation": SubResource("Animation_jddgd")
}

[node name="chapter 2" type="Node2D"]

[node name="Parallax2D" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.2, 0.2)
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("2_0rnry")

[node name="Parallax2D2" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.3, 0.3)
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D2"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("1_dwed3")

[node name="Parallax2D3" type="Parallax2D" parent="."]
scroll_scale = Vector2(0.6, 0.6)
repeat_size = Vector2(1280, 960)
repeat_times = 3

[node name="Sprite2D" type="Sprite2D" parent="Parallax2D3"]
position = Vector2(586.3334, 338.00003)
scale = Vector2(10, 10)
texture = ExtResource("3_l534y")

[node name="npc" parent="." instance=ExtResource("6_gaqhi")]
position = Vector2(1125, -426)
dialogue = Array[String](["anim:awake", "anim:idle", "0:Who are you? Why are you disturbing my peaceful sleep?", "p:Sorry to bother you, mister cat. Do you know a guy named Lim Wei Jen?", "0:Of course! I guard his secrets... His half-finished experiments... all safe with me.", "0:I do not just protect his secrets. I protect everyone's.", "0:For I am not the hero they deserve... but the hero they need.", "anim:smirk", "0:I'm batm- I mean Github", "anim:idle"])

[node name="CollisionShape2D" parent="npc" index="0"]
position = Vector2(-89, 10)
shape = SubResource("RectangleShape2D_uynys")

[node name="Sprite2D" parent="npc" index="1"]
sprite_frames = SubResource("SpriteFrames_gaqhi")
animation = &"idle"
autoplay = "default"

[node name="npc2" parent="." instance=ExtResource("6_gaqhi")]
position = Vector2(-215, -234)
dialogue = Array[String](["0:updating... please do not close your computer..."])

[node name="CollisionShape2D" parent="npc2" index="0"]
position = Vector2(0.5625, 10)
shape = SubResource("RectangleShape2D_gxtk0")

[node name="Sprite2D" parent="npc2" index="1"]
sprite_frames = SubResource("SpriteFrames_ksx6q")
autoplay = "default"
frame_progress = 0.24647188

[node name="npc3" parent="." instance=ExtResource("6_gaqhi")]
position = Vector2(528, -425)
dialogue = Array[String](["0:What version am I at again?"])

[node name="CollisionShape2D" parent="npc3" index="0"]
position = Vector2(44, 3)
shape = SubResource("RectangleShape2D_cun2x")

[node name="Sprite2D" parent="npc3" index="1"]
sprite_frames = SubResource("SpriteFrames_jkbbk")
autoplay = "default"

[node name="npc4" parent="." instance=ExtResource("6_gaqhi")]
position = Vector2(1955, -617)
dialogue = Array[String](["0:Why am I in a 2D space?"])

[node name="CollisionShape2D" parent="npc4" index="0"]
position = Vector2(44, 3)
shape = SubResource("RectangleShape2D_cun2x")

[node name="Sprite2D" parent="npc4" index="1"]
sprite_frames = SubResource("SpriteFrames_cun2x")
autoplay = "default"

[node name="npc5" parent="." instance=ExtResource("6_gaqhi")]
position = Vector2(1168, 439)
dialogue = Array[String](["anim:default", "0:work work work work work work work work work work"])

[node name="CollisionShape2D" parent="npc5" index="0"]
position = Vector2(-1.3046875, 3)
shape = SubResource("RectangleShape2D_27lui")

[node name="Sprite2D" parent="npc5" index="1"]
sprite_frames = SubResource("SpriteFrames_o43xw")
animation = &"new_animation"
autoplay = "new_animation"

[node name="npc6" parent="." node_paths=PackedStringArray("multiple_labels") instance=ExtResource("6_gaqhi")]
position = Vector2(1888, 440)
dialogue = Array[String](["0:I'm better!", "1:No, I'm better!", "2:Kids these days..."])
multiple_labels = [NodePath("Label"), NodePath("Label2"), NodePath("Label3")]

[node name="CollisionShape2D" parent="npc6" index="0"]
position = Vector2(152.65234, 3)
shape = SubResource("RectangleShape2D_t4wku")

[node name="Sprite2D" parent="npc6" index="1"]
position = Vector2(129, -1)
sprite_frames = SubResource("SpriteFrames_27lui")
autoplay = "default"

[node name="Sprite2D2" type="AnimatedSprite2D" parent="npc6"]
position = Vector2(3, -1)
scale = Vector2(5.25, 5.25)
sprite_frames = SubResource("SpriteFrames_1q64l")
autoplay = "default"

[node name="Sprite2D3" type="AnimatedSprite2D" parent="npc6"]
position = Vector2(286, -1)
scale = Vector2(5.25, 5.25)
sprite_frames = SubResource("SpriteFrames_rflqm")

[node name="Label2" type="Label" parent="npc6"]
z_index = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 118.0
offset_top = -93.0
offset_right = 139.0
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("16_rflqm")
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_056qb")
horizontal_alignment = 1

[node name="Label3" type="Label" parent="npc6"]
z_index = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 240.0
offset_top = -93.0
offset_right = 261.0
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("16_rflqm")
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_056qb")
horizontal_alignment = 1

[node name="TileMapLayer" type="TileMapLayer" parent="."]
scale = Vector2(6, 6)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_kn1i3")

[node name="TileMapLayer2" type="TileMapLayer" parent="."]
scale = Vector2(6, 6)
tile_map_data = PackedByteArray("AAD5/wAAAAAAAAIAABD5/wEAAAAAAAMAABAFAPz/AAACAAAAAAAGAPz/AAADAAAAAAAHAPz/AAADAAAAAAAIAPz/AAADAAAAAAAJAPz/AAADAAAAAAAKAPz/AAADAAAAAAALAPz/AAACAAAAABAPAPr/AAACAAAAAAAQAPr/AAADAAAAAAARAPr/AAADAAAAAAASAPr/AAADAAAAAAATAPr/AAADAAAAAAAUAPr/AAADAAAAAAAVAPr/AAADAAAAAAAWAPr/AAACAAAAABAcAPz/AAACAAAAABAbAPz/AAADAAAAAAAaAPz/AAADAAAAAAAZAPz/AAADAAAAAAAYAPz/AAACAAAAAAAiAPT/AAABAAIAAAAiAPX/AAABAAMAAAAiAPb/AAABAAMAAAAiAPf/AAABAAMAAAAiAPj/AAABAAMAAAAiAPn/AAABAAMAAAAiAPr/AAABAAMAAAAiAPv/AAABAAQAAAA=")
tile_set = SubResource("TileSet_kn1i3")

[node name="employees not working" parent="." instance=ExtResource("20_lgq8u")]
position = Vector2(2640, -415)

[node name="player" parent="." instance=ExtResource("1_kn1i3")]
position = Vector2(-528, 152)

[node name="next level teleporter" parent="." instance=ExtResource("21_4hcmd")]
position = Vector2(3289, -433)
next_level_scene = ExtResource("22_o8wi0")

[node name="CollisionShape2D" parent="next level teleporter" index="0"]
position = Vector2(21.5, -60.5)
shape = SubResource("RectangleShape2D_o8wi0")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="CanvasLayer"]
libraries = {
&"": SubResource("AnimationLibrary_t7dqo")
}
autoplay = "new_animation"

[node name="Label" type="Label" parent="CanvasLayer"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -261.0
offset_top = -141.5
offset_right = 261.0
offset_bottom = 141.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("16_rflqm")
theme_override_font_sizes/font_size = 100
text = "Chapter 2:
My Skills"
horizontal_alignment = 1

[editable path="npc"]
[editable path="npc2"]
[editable path="npc3"]
[editable path="npc4"]
[editable path="npc5"]
[editable path="npc6"]
[editable path="next level teleporter"]
